name: drivly
description: "A comprehensive Flutter application designed for drivers to track their performance, income, and spare parts maintenance."
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # State Management - Riverpod Ecosystem
  flutter_riverpod: ^2.6.1
  hooks_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  flutter_hooks: ^0.20.5

  # Database & Persistence
  drift: ^2.27.0
  sqlite3_flutter_libs: ^0.5.24
  path_provider: ^2.1.4
  path: ^1.9.0

  # UI & Visualization
  fl_chart: ^1.0.0
  shimmer: ^3.0.0
  google_fonts: ^6.2.1

  # Utilities
  uuid: ^4.5.1
  intl: ^0.19.0
  dartz: ^0.10.1

  # Code Generation Support
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.4

  # Code Generation
  build_runner: ^2.4.13
  riverpod_generator: ^2.6.1
  freezed: ^2.5.8
  json_serializable: ^6.8.0
  drift_dev: ^2.27.0

flutter:
  uses-material-design: true
  assets:
    - assets/icon/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/drivly_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icon/drivly_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icon/drivly_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icon/drivly_icon.png"
