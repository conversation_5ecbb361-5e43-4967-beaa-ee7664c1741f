include: package:flutter_lints/flutter.yaml

linter:
  rules:
    # Dart Style Rules
    - camel_case_types
    - camel_case_extensions
    - file_names
    - non_constant_identifier_names
    - constant_identifier_names

    # Design Rules
    - prefer_const_constructors
    - prefer_const_constructors_in_immutables
    - prefer_const_declarations
    - prefer_const_literals_to_create_immutables
    - use_key_in_widget_constructors

    # Error Rules
    - avoid_print
    - avoid_unnecessary_containers
    - avoid_web_libraries_in_flutter
    - no_logic_in_create_state
    - prefer_is_empty
    - prefer_is_not_empty
    - sized_box_for_whitespace
    - use_build_context_synchronously

    # Pub Rules
    - sort_pub_dependencies

    # Style Rules
    - prefer_single_quotes
    - require_trailing_commas
    - sort_child_properties_last

analyzer:
  errors:
    # Treat missing required parameters as errors
    missing_required_param: error
    missing_return: error
    # Ignore TODOs in development
    todo: ignore
    # Ignore deprecated member use warnings for now
    deprecated_member_use: ignore
    deprecated_member_use_from_same_package: ignore
  exclude:
    # Exclude generated files from analysis
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.gr.dart"
    - "lib/generated_plugin_registrant.dart"
