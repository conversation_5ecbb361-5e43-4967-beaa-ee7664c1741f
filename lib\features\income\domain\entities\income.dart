import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../core/models/sync_status.dart';

part 'income.freezed.dart';
part 'income.g.dart';

/// Payment method enumeration
enum PaymentMethod {
  gopay('GoPay'),
  bca('BCA'),
  cash('Cash'),
  ovo('OVO'),
  bri('BRI'),
  rekpon('Rekpon');

  const PaymentMethod(this.displayName);
  
  final String displayName;

  static PaymentMethod fromString(String value) {
    switch (value.toLowerCase()) {
      case 'gopay':
        return PaymentMethod.gopay;
      case 'bca':
        return PaymentMethod.bca;
      case 'cash':
        return PaymentMethod.cash;
      case 'ovo':
        return PaymentMethod.ovo;
      case 'bri':
        return PaymentMethod.bri;
      case 'rekpon':
        return PaymentMethod.rekpon;
      default:
        return PaymentMethod.cash;
    }
  }
}

/// Income entity representing a driver's income record
@freezed
class Income with _$Income {
  const factory Income({
    required String id,
    required DateTime date,
    required double grossIncome,
    required double mileage,
    required PaymentMethod paymentMethod,
    String? notes,
    required DateTime createdAt,
    required DateTime updatedAt,
    required SyncStatus syncStatus,
  }) = _Income;

  factory Income.fromJson(Map<String, dynamic> json) => _$IncomeFromJson(json);
}

/// Extension for computed properties
extension IncomeExtension on Income {
  /// Calculate net income (gross income minus estimated fuel cost)
  /// Assuming average fuel consumption of 1 liter per 10km and fuel price of 10,000 IDR per liter
  double get netIncome {
    const double fuelPricePerLiter = 10000.0; // IDR
    const double kmPerLiter = 10.0;
    final double fuelCost = (mileage / kmPerLiter) * fuelPricePerLiter;
    return grossIncome - fuelCost;
  }

  /// Get income per kilometer
  double get incomePerKm {
    return mileage > 0 ? grossIncome / mileage : 0.0;
  }

  /// Get net income per kilometer
  double get netIncomePerKm {
    return mileage > 0 ? netIncome / mileage : 0.0;
  }

  /// Check if this is a high-value trip (above average)
  bool get isHighValue {
    return grossIncome > 50000; // Above 50k IDR
  }

  /// Get formatted date string
  String get formattedDate {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Get formatted time string
  String get formattedTime {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Check if the record was created today
  bool get isToday {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }

  /// Check if the record was created this week
  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    return date.isAfter(startOfWeek.subtract(const Duration(days: 1)));
  }
}
