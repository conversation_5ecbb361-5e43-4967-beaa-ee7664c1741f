import '../../../../core/utils/result.dart';
import '../entities/income.dart';

/// Abstract repository interface for income operations
abstract class IncomeRepository {
  /// Get paginated list of income records
  Future<Result<List<Income>>> getIncomeRecords({
    int? limit,
    int? offset,
    DateTime? startDate,
    DateTime? endDate,
    PaymentMethod? paymentMethod,
    String? orderBy,
    bool ascending = false,
  });

  /// Get income record by ID
  Future<Result<Income?>> getIncomeById(String id);

  /// Create new income record
  Future<Result<String>> createIncome(Income income);

  /// Update existing income record
  Future<Result<bool>> updateIncome(Income income);

  /// Delete income record
  Future<Result<bool>> deleteIncome(String id);

  /// Get income statistics for a date range
  Future<Result<IncomeStats>> getIncomeStats({
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get records that need synchronization
  Future<Result<List<Income>>> getUnsyncedRecords();

  /// Mark record as synced
  Future<Result<bool>> markAsSynced(String id);

  /// Get daily income data for charts
  Future<Result<List<DailyIncomeData>>> getDailyIncomeData({
    required DateTime startDate,
    required DateTime endDate,
  });

  /// Get weekly income data for charts
  Future<Result<List<WeeklyIncomeData>>> getWeeklyIncomeData({
    required DateTime startDate,
    required DateTime endDate,
  });
}

/// Income statistics data class
class IncomeStats {
  final double totalGrossIncome;
  final double totalMileage;
  final int totalTrips;
  final double averageIncomePerTrip;
  final double averageIncomePerKm;
  final double totalNetIncome;

  const IncomeStats({
    required this.totalGrossIncome,
    required this.totalMileage,
    required this.totalTrips,
    required this.averageIncomePerTrip,
    required this.averageIncomePerKm,
    required this.totalNetIncome,
  });

  factory IncomeStats.fromMap(Map<String, dynamic> map) {
    final totalGrossIncome =
        (map['totalGrossIncome'] as num?)?.toDouble() ?? 0.0;
    final totalMileage = (map['totalMileage'] as num?)?.toDouble() ?? 0.0;
    final totalTrips = (map['totalTrips'] as num?)?.toInt() ?? 0;

    return IncomeStats(
      totalGrossIncome: totalGrossIncome,
      totalMileage: totalMileage,
      totalTrips: totalTrips,
      averageIncomePerTrip: totalTrips > 0
          ? totalGrossIncome / totalTrips
          : 0.0,
      averageIncomePerKm: totalMileage > 0
          ? totalGrossIncome / totalMileage
          : 0.0,
      totalNetIncome: _calculateNetIncome(totalGrossIncome, totalMileage),
    );
  }

  static double _calculateNetIncome(double grossIncome, double mileage) {
    const double fuelPricePerLiter = 10000.0; // IDR
    const double kmPerLiter = 10.0;
    final double fuelCost = (mileage / kmPerLiter) * fuelPricePerLiter;
    return grossIncome - fuelCost;
  }
}

/// Daily income data for charts
class DailyIncomeData {
  final DateTime date;
  final double grossIncome;
  final double netIncome;
  final double mileage;
  final int tripCount;

  const DailyIncomeData({
    required this.date,
    required this.grossIncome,
    required this.netIncome,
    required this.mileage,
    required this.tripCount,
  });
}

/// Weekly income data for charts
class WeeklyIncomeData {
  final DateTime weekStart;
  final DateTime weekEnd;
  final double grossIncome;
  final double netIncome;
  final double mileage;
  final int tripCount;

  const WeeklyIncomeData({
    required this.weekStart,
    required this.weekEnd,
    required this.grossIncome,
    required this.netIncome,
    required this.mileage,
    required this.tripCount,
  });
}
