/// Enumeration for synchronization status of database records
enum SyncStatus {
  /// Record needs to be uploaded to cloud
  pendingUpload('pendingUpload'),
  
  /// Record is successfully synchronized
  synced('synced'),
  
  /// Sync conflict detected
  conflict('conflict'),
  
  /// Sync error occurred
  error('error');

  const SyncStatus(this.value);
  
  final String value;

  /// Convert string value to SyncStatus enum
  static SyncStatus fromString(String value) {
    switch (value) {
      case 'pendingUpload':
        return SyncStatus.pendingUpload;
      case 'synced':
        return SyncStatus.synced;
      case 'conflict':
        return SyncStatus.conflict;
      case 'error':
        return SyncStatus.error;
      default:
        return SyncStatus.pendingUpload;
    }
  }

  /// Get display name for the sync status
  String get displayName {
    switch (this) {
      case SyncStatus.pendingUpload:
        return 'Pending Upload';
      case SyncStatus.synced:
        return 'Synced';
      case SyncStatus.conflict:
        return 'Conflict';
      case SyncStatus.error:
        return 'Error';
    }
  }

  /// Check if the record needs synchronization
  bool get needsSync {
    return this == SyncStatus.pendingUpload || this == SyncStatus.error;
  }

  /// Check if the record is successfully synced
  bool get isSynced {
    return this == SyncStatus.synced;
  }

  /// Check if there's a sync conflict
  bool get hasConflict {
    return this == SyncStatus.conflict;
  }
}
