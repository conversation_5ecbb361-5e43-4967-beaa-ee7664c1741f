import '../errors/failures.dart';

/// A generic result type that can represent either success or failure
sealed class Result<T> {
  const Result();
}

/// Represents a successful result with data
class Success<T> extends Result<T> {
  final T data;
  
  const Success(this.data);
  
  @override
  String toString() => 'Success($data)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Success<T> && other.data == data;
  }
  
  @override
  int get hashCode => data.hashCode;
}

/// Represents a failed result with error information
class Error<T> extends Result<T> {
  final Failure failure;
  
  const Error(this.failure);
  
  @override
  String toString() => 'Error($failure)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Error<T> && other.failure == failure;
  }
  
  @override
  int get hashCode => failure.hashCode;
}

/// Extension methods for Result
extension ResultExtension<T> on Result<T> {
  /// Check if the result is successful
  bool get isSuccess => this is Success<T>;
  
  /// Check if the result is an error
  bool get isError => this is Error<T>;
  
  /// Get the data if successful, null otherwise
  T? get dataOrNull {
    return switch (this) {
      Success<T> success => success.data,
      Error<T> _ => null,
    };
  }
  
  /// Get the failure if error, null otherwise
  Failure? get failureOrNull {
    return switch (this) {
      Success<T> _ => null,
      Error<T> error => error.failure,
    };
  }
  
  /// Transform the data if successful
  Result<R> map<R>(R Function(T) transform) {
    return switch (this) {
      Success<T> success => Success(transform(success.data)),
      Error<T> error => Error<R>(error.failure),
    };
  }
  
  /// Transform the data if successful, or return the error
  Result<R> flatMap<R>(Result<R> Function(T) transform) {
    return switch (this) {
      Success<T> success => transform(success.data),
      Error<T> error => Error<R>(error.failure),
    };
  }
  
  /// Execute different functions based on the result
  R fold<R>(
    R Function(Failure) onError,
    R Function(T) onSuccess,
  ) {
    return switch (this) {
      Success<T> success => onSuccess(success.data),
      Error<T> error => onError(error.failure),
    };
  }
  
  /// Get the data or throw an exception
  T get dataOrThrow {
    return switch (this) {
      Success<T> success => success.data,
      Error<T> error => throw Exception(error.failure.message),
    };
  }
  
  /// Get the data or return a default value
  T getOrElse(T defaultValue) {
    return switch (this) {
      Success<T> success => success.data,
      Error<T> _ => defaultValue,
    };
  }
}
