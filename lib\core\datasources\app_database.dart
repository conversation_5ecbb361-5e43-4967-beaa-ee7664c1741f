import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import '../../features/income/data/datasources/income_table.dart';

part 'app_database.g.dart';

/// Main application database using Drift ORM
@DriftDatabase(tables: [IncomeTable])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        // Handle future schema migrations here
        if (from < 2) {
          // Example: await m.addColumn(incomeTable, incomeTable.newColumn);
        }
      },
      beforeOpen: (details) async {
        // Enable foreign key constraints
        await customStatement('PRAGMA foreign_keys = ON');
        
        // Optimize SQLite performance
        await customStatement('PRAGMA journal_mode = WAL');
        await customStatement('PRAGMA synchronous = NORMAL');
        await customStatement('PRAGMA cache_size = 10000');
        await customStatement('PRAGMA temp_store = MEMORY');
      },
    );
  }

  /// Get all income records with optional filtering and pagination
  Future<List<IncomeTableData>> getIncomeRecords({
    int? limit,
    int? offset,
    DateTime? startDate,
    DateTime? endDate,
    String? paymentMethod,
    String? orderBy = 'date',
    bool ascending = false,
  }) async {
    final query = select(incomeTable);

    // Apply filters
    if (startDate != null) {
      query.where((tbl) => tbl.date.isAfterOrEqualValue(startDate));
    }
    if (endDate != null) {
      query.where((tbl) => tbl.date.isBeforeOrEqualValue(endDate));
    }
    if (paymentMethod != null) {
      query.where((tbl) => tbl.paymentMethod.equals(paymentMethod));
    }

    // Apply ordering
    switch (orderBy) {
      case 'date':
        query.orderBy([
          (tbl) => ascending 
            ? OrderingTerm.asc(tbl.date) 
            : OrderingTerm.desc(tbl.date)
        ]);
        break;
      case 'grossIncome':
        query.orderBy([
          (tbl) => ascending 
            ? OrderingTerm.asc(tbl.grossIncome) 
            : OrderingTerm.desc(tbl.grossIncome)
        ]);
        break;
      case 'mileage':
        query.orderBy([
          (tbl) => ascending 
            ? OrderingTerm.asc(tbl.mileage) 
            : OrderingTerm.desc(tbl.mileage)
        ]);
        break;
    }

    // Apply pagination
    if (limit != null) {
      query.limit(limit, offset: offset);
    }

    return query.get();
  }

  /// Get income record by ID
  Future<IncomeTableData?> getIncomeById(String id) async {
    final query = select(incomeTable)..where((tbl) => tbl.id.equals(id));
    return query.getSingleOrNull();
  }

  /// Insert new income record
  Future<int> insertIncome(IncomeTableCompanion income) async {
    return into(incomeTable).insert(income);
  }

  /// Update existing income record
  Future<bool> updateIncome(String id, IncomeTableCompanion income) async {
    final result = await (update(incomeTable)..where((tbl) => tbl.id.equals(id)))
        .write(income);
    return result > 0;
  }

  /// Delete income record
  Future<bool> deleteIncome(String id) async {
    final result = await (delete(incomeTable)..where((tbl) => tbl.id.equals(id)))
        .go();
    return result > 0;
  }

  /// Get income statistics for a date range
  Future<Map<String, dynamic>> getIncomeStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final query = selectOnly(incomeTable)
      ..addColumns([
        incomeTable.grossIncome.sum(),
        incomeTable.mileage.sum(),
        incomeTable.id.count(),
      ]);

    if (startDate != null) {
      query.where(incomeTable.date.isAfterOrEqualValue(startDate));
    }
    if (endDate != null) {
      query.where(incomeTable.date.isBeforeOrEqualValue(endDate));
    }

    final result = await query.getSingle();
    
    return {
      'totalGrossIncome': result.read(incomeTable.grossIncome.sum()) ?? 0.0,
      'totalMileage': result.read(incomeTable.mileage.sum()) ?? 0.0,
      'totalTrips': result.read(incomeTable.id.count()) ?? 0,
    };
  }

  /// Get records that need synchronization
  Future<List<IncomeTableData>> getUnsyncedRecords() async {
    final query = select(incomeTable)
      ..where((tbl) => tbl.syncStatus.isIn(['pendingUpload', 'error']));
    return query.get();
  }
}

/// Database connection setup
LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'drivly.db'));
    return NativeDatabase(file);
  });
}
