import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/datasources/app_database.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/models/sync_status.dart';
import '../../../../core/utils/result.dart';
import '../../domain/entities/income.dart';
import '../../domain/repositories/income_repository.dart';
import '../datasources/income_table.dart';

/// Concrete implementation of IncomeRepository
class IncomeRepositoryImpl implements IncomeRepository {
  final AppDatabase _database;
  final Uuid _uuid;

  const IncomeRepositoryImpl({
    required AppDatabase database,
    required Uuid uuid,
  }) : _database = database,
       _uuid = uuid;

  @override
  Future<Result<List<Income>>> getIncomeRecords({
    int? limit,
    int? offset,
    DateTime? startDate,
    DateTime? endDate,
    PaymentMethod? paymentMethod,
    String? orderBy,
    bool ascending = false,
  }) async {
    try {
      final records = await _database.getIncomeRecords(
        limit: limit,
        offset: offset,
        startDate: startDate,
        endDate: endDate,
        paymentMethod: paymentMethod?.name,
        orderBy: orderBy,
        ascending: ascending,
      );

      final incomes = records.map((record) => record.toDomain()).toList();
      return Success(incomes);
    } on DatabaseException catch (e) {
      return Error(Failure.database(e.message));
    } catch (e) {
      return Error(Failure.unexpected(e.toString()));
    }
  }

  @override
  Future<Result<Income?>> getIncomeById(String id) async {
    try {
      final record = await _database.getIncomeById(id);
      return Success(record?.toDomain());
    } on DatabaseException catch (e) {
      return Error(Failure.database(e.message));
    } catch (e) {
      return Error(Failure.unexpected(e.toString()));
    }
  }

  @override
  Future<Result<String>> createIncome(Income income) async {
    try {
      // Generate new ID if not provided
      final id = income.id.isEmpty ? _uuid.v4() : income.id;

      final newIncome = income.copyWith(
        id: id,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        syncStatus: SyncStatus.pendingUpload,
      );

      await _database.insertIncome(newIncome.toCompanion());
      return Success(id);
    } on DatabaseException catch (e) {
      return Error(Failure.database(e.message));
    } catch (e) {
      return Error(Failure.unexpected(e.toString()));
    }
  }

  @override
  Future<Result<bool>> updateIncome(Income income) async {
    try {
      final updatedIncome = income.copyWith(
        updatedAt: DateTime.now(),
        syncStatus: SyncStatus.pendingUpload,
      );

      final success = await _database.updateIncome(
        income.id,
        updatedIncome.toUpdateCompanion(),
      );
      return Success(success);
    } on DatabaseException catch (e) {
      return Error(Failure.database(e.message));
    } catch (e) {
      return Error(Failure.unexpected(e.toString()));
    }
  }

  @override
  Future<Result<bool>> deleteIncome(String id) async {
    try {
      final success = await _database.deleteIncome(id);
      return Success(success);
    } on DatabaseException catch (e) {
      return Error(Failure.database(e.message));
    } catch (e) {
      return Error(Failure.unexpected(e.toString()));
    }
  }

  @override
  Future<Result<IncomeStats>> getIncomeStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final statsMap = await _database.getIncomeStats(
        startDate: startDate,
        endDate: endDate,
      );

      final stats = IncomeStats.fromMap(statsMap);
      return Success(stats);
    } on DatabaseException catch (e) {
      return Error(Failure.database(e.message));
    } catch (e) {
      return Error(Failure.unexpected(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Income>>> getUnsyncedRecords() async {
    try {
      final records = await _database.getUnsyncedRecords();
      final incomes = records.map((record) => record.toDomain()).toList();
      return Right(incomes);
    } on DatabaseException catch (e) {
      return Left(Failure.database(e.message));
    } catch (e) {
      return Left(Failure.unexpected(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> markAsSynced(String id) async {
    try {
      final companion = IncomeTableCompanion(
        syncStatus: Value(SyncStatus.synced.value),
        updatedAt: Value(DateTime.now()),
      );

      final success = await _database.updateIncome(id, companion);
      return Right(success);
    } on DatabaseException catch (e) {
      return Left(Failure.database(e.message));
    } catch (e) {
      return Left(Failure.unexpected(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<DailyIncomeData>>> getDailyIncomeData({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      // This would typically involve a more complex query
      // For now, we'll get all records and group them by day
      final records = await _database.getIncomeRecords(
        startDate: startDate,
        endDate: endDate,
        orderBy: 'date',
        ascending: true,
      );

      final dailyData = <DateTime, DailyIncomeData>{};

      for (final record in records) {
        final income = record.toDomain();
        final dateKey = DateTime(
          income.date.year,
          income.date.month,
          income.date.day,
        );

        if (dailyData.containsKey(dateKey)) {
          final existing = dailyData[dateKey]!;
          dailyData[dateKey] = DailyIncomeData(
            date: dateKey,
            grossIncome: existing.grossIncome + income.grossIncome,
            netIncome: existing.netIncome + income.netIncome,
            mileage: existing.mileage + income.mileage,
            tripCount: existing.tripCount + 1,
          );
        } else {
          dailyData[dateKey] = DailyIncomeData(
            date: dateKey,
            grossIncome: income.grossIncome,
            netIncome: income.netIncome,
            mileage: income.mileage,
            tripCount: 1,
          );
        }
      }

      final sortedData = dailyData.values.toList()
        ..sort((a, b) => a.date.compareTo(b.date));

      return Right(sortedData);
    } on DatabaseException catch (e) {
      return Left(Failure.database(e.message));
    } catch (e) {
      return Left(Failure.unexpected(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<WeeklyIncomeData>>> getWeeklyIncomeData({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final dailyDataResult = await getDailyIncomeData(
        startDate: startDate,
        endDate: endDate,
      );

      return dailyDataResult.fold((failure) => Left(failure), (dailyData) {
        final weeklyData = <DateTime, WeeklyIncomeData>{};

        for (final daily in dailyData) {
          final weekStart = _getWeekStart(daily.date);
          final weekEnd = weekStart.add(const Duration(days: 6));

          if (weeklyData.containsKey(weekStart)) {
            final existing = weeklyData[weekStart]!;
            weeklyData[weekStart] = WeeklyIncomeData(
              weekStart: weekStart,
              weekEnd: weekEnd,
              grossIncome: existing.grossIncome + daily.grossIncome,
              netIncome: existing.netIncome + daily.netIncome,
              mileage: existing.mileage + daily.mileage,
              tripCount: existing.tripCount + daily.tripCount,
            );
          } else {
            weeklyData[weekStart] = WeeklyIncomeData(
              weekStart: weekStart,
              weekEnd: weekEnd,
              grossIncome: daily.grossIncome,
              netIncome: daily.netIncome,
              mileage: daily.mileage,
              tripCount: daily.tripCount,
            );
          }
        }

        final sortedData = weeklyData.values.toList()
          ..sort((a, b) => a.weekStart.compareTo(b.weekStart));

        return Right(sortedData);
      });
    } catch (e) {
      return Left(Failure.unexpected(e.toString()));
    }
  }

  /// Get the start of the week (Monday) for a given date
  DateTime _getWeekStart(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return DateTime(
      date.year,
      date.month,
      date.day,
    ).subtract(Duration(days: daysFromMonday));
  }
}
