import 'package:drift/drift.dart';
import '../../../../core/models/sync_status.dart';
import '../../domain/entities/income.dart';

/// Drift table definition for income records
class IncomeTable extends Table {
  @override
  String get tableName => 'income';

  /// Primary key - UUID string
  TextColumn get id => text()();
  
  /// Date and time of the income record
  DateTimeColumn get date => dateTime()();
  
  /// Gross income amount in IDR
  RealColumn get grossIncome => real()();
  
  /// Distance traveled in kilometers
  RealColumn get mileage => real()();
  
  /// Payment method used
  TextColumn get paymentMethod => text()();
  
  /// Optional notes
  TextColumn get notes => text().nullable()();
  
  /// Record creation timestamp
  DateTimeColumn get createdAt => dateTime()();
  
  /// Record last update timestamp
  DateTimeColumn get updatedAt => dateTime()();
  
  /// Synchronization status
  TextColumn get syncStatus => text()();

  @override
  Set<Column> get primaryKey => {id};

  @override
  List<String> get customConstraints => [
    // Ensure gross income is positive
    'CHECK (gross_income > 0)',
    // Ensure mileage is positive
    'CHECK (mileage > 0)',
    // Ensure valid payment methods
    'CHECK (payment_method IN (\'GoPay\', \'BCA\', \'Cash\', \'OVO\', \'BRI\', \'Rekpon\'))',
    // Ensure valid sync status
    'CHECK (sync_status IN (\'pendingUpload\', \'synced\', \'conflict\', \'error\'))',
  ];
}

/// Extension to convert between Drift data and domain entities
extension IncomeTableExtension on IncomeTableData {
  /// Convert Drift data to domain entity
  Income toDomain() {
    return Income(
      id: id,
      date: date,
      grossIncome: grossIncome,
      mileage: mileage,
      paymentMethod: PaymentMethod.fromString(paymentMethod),
      notes: notes,
      createdAt: createdAt,
      updatedAt: updatedAt,
      syncStatus: SyncStatus.fromString(syncStatus),
    );
  }
}

/// Extension to convert domain entity to Drift companion
extension IncomeEntityExtension on Income {
  /// Convert domain entity to Drift companion for inserts
  IncomeTableCompanion toCompanion() {
    return IncomeTableCompanion(
      id: Value(id),
      date: Value(date),
      grossIncome: Value(grossIncome),
      mileage: Value(mileage),
      paymentMethod: Value(paymentMethod.name),
      notes: Value(notes),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      syncStatus: Value(syncStatus.value),
    );
  }

  /// Convert domain entity to Drift companion for updates
  IncomeTableCompanion toUpdateCompanion() {
    return IncomeTableCompanion(
      date: Value(date),
      grossIncome: Value(grossIncome),
      mileage: Value(mileage),
      paymentMethod: Value(paymentMethod.name),
      notes: Value(notes),
      updatedAt: Value(DateTime.now()),
      syncStatus: Value(syncStatus.value),
    );
  }
}
