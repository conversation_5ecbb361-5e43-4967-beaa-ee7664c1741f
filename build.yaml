targets:
  $default:
    builders:
      drift_dev:
        options:
          store_date_time_values_as_text: true
      riverpod_generator:
        options:
          # Generate providers with proper naming
          generate_riverpod_annotation: true
      freezed:
        options:
          # Generate copyWith and toString methods
          generate_copy_with: true
          generate_to_string: true
      json_serializable:
        options:
          # Generate both fromJson and toJson methods
          generate_to_json: true
          generate_from_json: true
          # Use explicit field names
          explicit_to_json: true
