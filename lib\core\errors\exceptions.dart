/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;
  
  const AppException(this.message, [this.code]);
  
  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Server-related exceptions
class ServerException extends AppException {
  const ServerException(super.message, [super.code]);
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, [super.code]);
}

/// Database-related exceptions
class DatabaseException extends AppException {
  const DatabaseException(super.message, [super.code]);
}

/// Cache-related exceptions
class CacheException extends AppException {
  const CacheException(super.message, [super.code]);
}

/// Input validation exceptions
class ValidationException extends AppException {
  const ValidationException(super.message, [super.code]);
}

/// Business logic exceptions
class BusinessLogicException extends AppException {
  const BusinessLogicException(super.message, [super.code]);
}

/// Resource not found exceptions
class NotFoundException extends AppException {
  const NotFoundException(super.message, [super.code]);
}

/// Utility function to convert exceptions to failures
import 'failures.dart';

Failure exceptionToFailure(Exception exception) {
  if (exception is DatabaseException) {
    return Failure.database(exception.message);
  } else if (exception is NetworkException) {
    return Failure.network(exception.message);
  } else if (exception is ServerException) {
    return Failure.server(exception.message);
  } else if (exception is CacheException) {
    return Failure.cache(exception.message);
  } else if (exception is ValidationException) {
    return Failure.invalidInput(exception.message);
  } else if (exception is BusinessLogicException) {
    return Failure.businessLogic(exception.message);
  } else if (exception is NotFoundException) {
    return Failure.notFound(exception.message);
  }
  return Failure.unexpected(exception.toString());
}
