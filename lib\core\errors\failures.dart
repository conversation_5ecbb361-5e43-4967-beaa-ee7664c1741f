import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

/// Base failure class for error handling throughout the application
@freezed
class Failure with _$Failure {
  const factory Failure.server([String? message]) = ServerFailure;
  const factory Failure.cache([String? message]) = CacheFailure;
  const factory Failure.network([String? message]) = NetworkFailure;
  const factory Failure.invalidInput([String? message]) = InvalidInputFailure;
  const factory Failure.database([String? message]) = DatabaseFailure;
  const factory Failure.businessLogic([String? message]) = BusinessLogicFailure;
  const factory Failure.notFound([String? message]) = NotFoundFailure;
  const factory Failure.unexpected([String? message]) = UnexpectedFailure;
}

/// Extension to get user-friendly error messages
extension FailureExtension on Failure {
  String get message {
    return when(
      server: (message) => message ?? 'Server error occurred',
      cache: (message) => message ?? 'Cache error occurred',
      network: (message) => message ?? 'Network connection error',
      invalidInput: (message) => message ?? 'Invalid input provided',
      database: (message) => message ?? 'Database error occurred',
      businessLogic: (message) => message ?? 'Business logic error',
      notFound: (message) => message ?? 'Resource not found',
      unexpected: (message) => message ?? 'An unexpected error occurred',
    );
  }
}
